<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Edge Function</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #2980b9;
        }
        .result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #3498db;
        }
        .error {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .success {
            border-left-color: #27ae60;
            background: #f2fdf2;
        }
    </style>
</head>
<body>
    <h1>Test de Edge Function - Generación de Documentos</h1>
    
    <div class="test-container">
        <h2>Prueba de la Edge Function</h2>
        <p>Esta página te permite probar la nueva Edge Function para generar documentos.</p>
        
        <label for="recordId">ID del registro (enlace):</label>
        <input type="text" id="recordId" placeholder="Introduce el ID del registro" style="width: 300px; padding: 8px; margin: 10px 0;">
        
        <br>
        <button onclick="testEdgeFunction()">Probar Edge Function</button>
        <button onclick="testDirectCall()">Probar Llamada Directa</button>
        
        <div id="results"></div>
    </div>

    <script>
        const SUPABASE_URL = 'https://twxtlrjbpxifcdzepxfk.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR3eHRscmpicHhpZmNkemVweGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTk0MzAwODgsImV4cCI6MjA3NTAwNjA4OH0.ycUkKeHQ7c2bPBA01EN1bi70kIEfS7D_Uc4K6FA1YNA';

        function addResult(message, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testEdgeFunction() {
            const recordId = document.getElementById('recordId').value;
            
            if (!recordId) {
                addResult('Por favor, introduce un ID de registro', 'error');
                return;
            }

            addResult('Iniciando prueba de Edge Function...', 'result');

            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/generate-document`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    },
                    body: JSON.stringify({
                        recordId: recordId
                    })
                });

                addResult(`Respuesta HTTP: ${response.status} ${response.statusText}`, 'result');

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Error ${response.status}: ${errorText}`);
                }

                const result = await response.json();
                
                if (result.success) {
                    addResult(`✅ Documento generado exitosamente: ${result.fileName}`, 'success');
                    addResult(`📁 URL de descarga: <a href="${result.downloadUrl}" target="_blank">Descargar documento</a>`, 'success');
                    
                    // Intentar descargar automáticamente
                    const downloadLink = document.createElement('a');
                    downloadLink.href = result.downloadUrl;
                    downloadLink.download = result.fileName;
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                    
                } else {
                    addResult(`❌ Error: ${result.error}`, 'error');
                }

            } catch (error) {
                addResult(`❌ Error en la prueba: ${error.message}`, 'error');
                console.error('Error completo:', error);
            }
        }

        async function testDirectCall() {
            addResult('Probando conexión directa a Supabase...', 'result');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/Exp_Advos?select=*&limit=1`, {
                    headers: {
                        'apikey': SUPABASE_ANON_KEY,
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Conexión a Supabase exitosa. Registros encontrados: ${data.length}`, 'success');
                    if (data.length > 0) {
                        addResult(`📋 Primer registro: ${JSON.stringify(data[0], null, 2)}`, 'result');
                    }
                } else {
                    addResult(`❌ Error de conexión: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Error de red: ${error.message}`, 'error');
            }
        }

        // Auto-llenar con un ID de ejemplo si está disponible
        window.addEventListener('load', () => {
            addResult('🚀 Página de pruebas cargada. Introduce un ID de registro para probar.', 'result');
        });
    </script>
</body>
</html>
