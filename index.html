<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visor de Datos de Expedientes</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- <PERSON><PERSON> (Sidebar) -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>Navegación</h2>
            </div>
            <nav class="sidebar-nav">
                <button id="nav-search-btn" class="nav-main-button">🔍 Búsqueda General</button>
                <button id="nav-add-btn" class="nav-main-button">➕ Añadir Registro</button>
                <hr>
                <p class="nav-title">Ver Tablas Completas</p>
                <button data-table="Datos_Generales" class="nav-table-btn">Datos Generales</button>
                <button data-table="Exp_Advos" class="nav-table-btn">Exp. Administrativos</button>
                <button data-table="Nacionalidad" class="nav-table-btn">Nacionalidad</button>
                <button data-table="Proc_Judiciales" class="nav-table-btn">Proc. Judiciales</button>
                
            </nav>
        </aside>

        <!-- Contenido Principal -->
        <main class="main-content">
            <header class="main-header">
                <button id="sidebar-toggle">☰</button>
                <h1>Visor de Base de Datos</h1>
            </header>
            
            <!-- ================================================================ -->
            <!-- ===== INICIO: SECCIÓN DE BÚSQUEDA (CÓDIGO AÑADIDO) ============= -->
            <!-- ================================================================ -->
            <div id="search-page">
                <section>
                    <h2>Búsqueda General</h2>
                    <p>Busca por ID o por nombre en una o todas las tablas.</p>
                </section>
                <form id="search-form" class="search-form">
                    <div class="form-group">
                        <label for="search-type">Tipo de Expediente</label>
                        <select id="search-type" name="type">
                            <option value="Todos">Todos</option>
                            <option value="Datos_Generales">General</option>
                            <option value="Exp_Advos">Administrativo</option>
                            <option value="Nacionalidad">Nacionalidad</option>
                            <option value="Proc_Judiciales">Judicial</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="search-id">Buscar por ID (NIE / Pasaporte)</label>
                        <input type="text" id="search-id" name="id" placeholder="Introduzca NIE o Pasaporte...">
                    </div>
                    <div class="form-group">
                        <label for="search-name">Buscar por Nombre</label>
                        <input type="text" id="search-name" name="name" placeholder="Introduzca nombre y/o apellidos...">
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Buscar</button>
                        <button type="button" id="clear-search" class="btn-secondary">Limpiar</button>
                    </div>
                </form>

                <!-- Contenedor para los resultados -->
                <div class="search-results-container">
                    <div id="search-loader" class="loader hidden">Buscando...</div>
                    <p id="search-message"></p>
                    <div id="search-results"></div>
                </div>
            </div>
            <!-- ================================================================ -->
            <!-- ===== FIN: SECCIÓN DE BÚSQUEDA ================================= -->
            <!-- ================================================================ -->


            <!-- CONTENEDOR DEL VISOR DE TABLAS -->
            <div id="table-viewer-page" class="hidden">
                <p>Selecciona una tabla del menú lateral para ver su contenido.</p>
                <section id="data-display">
                    <h2 id="table-name"></h2>
                    <div id="loader" class="loader hidden">Cargando...</div>
                    <div id="table-container"></div>
                </section>
            </div>


            <!-- ================================================================ -->
            <!-- ===== INICIO: SECCIÓN AÑADIR REGISTRO (CÓDIGO AÑADIDO) ========= -->
            <!-- ================================================================ -->
              <div id="add-record-page" class="hidden">
                 <section>
                    <h2>Añadir Nuevo Registro</h2>
                    <p>Selecciona la tabla y completa los campos para añadir un nuevo registro.</p>
                </section>

                <form id="add-record-form" class="add-form">
                    <!-- PASO 1: Selección de Tabla (Ahora en su propia sección) -->
                    <div class="form-group table-selection-group">
                        <label for="add-table-select">Seleccionar Tabla donde guardar</label>
                        <select id="add-table-select" name="table">
                            <option value="Exp_Advos">Exp. Administrativos</option>
                            <option value="Nacionalidad">Nacionalidad</option>
                            <option value="Proc_Judiciales">Proc. Judiciales</option>
                            <option value="Datos_Generales">Datos Generales</option>
                        </select>
                    </div>

                    <!-- Línea divisoria para separar la selección de los campos -->
                    <hr class="form-divider">
                    
                    <!-- PASO 2: Contenedor para los campos (El CSS lo pondrá en 3 columnas) -->
                    <div id="form-fields-container">
                        <!-- Los campos del formulario se generarán aquí por JS -->
                    </div>
                    
                    <!-- Indicadores y Acciones -->
                    <div id="add-loader" class="loader hidden">Guardando...</div>
                    <p id="add-message"></p>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Guardar Registro</button>
                        <button type="button" id="clear-form-btn" class="btn-secondary">Limpiar Formulario</button>
                    </div>
                </form>
            </div>
            <!-- ================================================================ -->
            <!-- ===== FIN: SECCIÓN AÑADIR REGISTRO ============================= -->
            <!-- ================================================================ -->

        </main>
    </div>

    <!-- Modal de Edición (sin cambios) -->
    <div id="edit-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <header class="modal-header">
                <h2>Editar Registro</h2>
                <button id="close-modal-btn" class="close-button">&times;</button>
            </header>
            <div class="modal-body">
                <form id="edit-form">
                    <div id="edit-form-fields" class="form-fields-grid">
                        <!-- Los campos del formulario de edición se generarán aquí -->
                    </div>
                    <div class="form-actions modal-footer">
                        <button type="submit" id="save-changes-btn" class="btn-primary">Guardar Cambios</button>
                        <button type="button" id="cancel-edit-btn" class="btn-secondary">Cancelar</button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Scripts -->
    <!-- Estas librerías deben cargarse en este orden y ANTES de tus propios scripts -->
    <script src="https://cdn.jsdelivr.net/npm/pizzip@3.1.4/dist/pizzip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/docxtemplater/3.47.1/docxtemplater.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Tus scripts -->
    <script src="script.js"></script>
    <script src="search.js"></script>
    <script src="add-record.js"></script>
    <script src="document-generator.js"></script>
    
</body>
</html>