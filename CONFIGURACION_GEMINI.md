# Configuración de Gemini API para Edge Function

## Pasos para configurar la API key de Gemini:

### 1. Obtener API Key de Gemini
1. Ve a [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Inicia sesión con tu cuenta de Google
3. Crea una nueva API key
4. Copia la API key generada

### 2. Configurar en Supabase
1. Ve al dashboard de Supabase: https://supabase.com/dashboard/project/twxtlrjbpxifcdzepxfk/functions
2. En el menú lateral, ve a "Edge Functions"
3. Haz clic en "Manage secrets" o "Environment variables"
4. Añade una nueva variable:
   - **Nombre**: `GEMINI_API_KEY`
   - **Valor**: Tu API key de Gemini (la que copiaste en el paso 1)
5. Guarda los cambios

### 3. Verificar la configuración
Una vez configurada la API key, la Edge Function podrá:
- Generar contenido inteligente usando Gemini AI
- Crear documentos personalizados basados en los datos de la tabla
- Mejorar automáticamente el formato y contenido del documento

### 4. Probar la funcionalidad
1. Ve a tu aplicación web
2. Navega a la tabla "Exp_Advos"
3. Haz clic en "Generar Doc" en cualquier registro
4. El sistema debería generar un documento usando IA y descargarlo automáticamente

## Características de la nueva implementación:

### ✅ Ventajas sobre docxtemplater:
- **IA integrada**: Gemini mejora el contenido del documento
- **Sin errores de tags**: No hay problemas con delimitadores malformados
- **Procesamiento en servidor**: Más seguro y eficiente
- **Contenido inteligente**: La IA puede adaptar el texto según el contexto
- **Escalabilidad**: Funciona mejor con grandes volúmenes de datos

### 🔧 Funcionalidades implementadas:
- Generación automática de documentos de representación legal
- Integración con datos de la tabla Exp_Advos
- Descarga automática del documento generado
- Almacenamiento en Supabase Storage
- Manejo de errores mejorado

### 📋 Próximos pasos opcionales:
- Añadir más plantillas de documentos
- Implementar generación de PDFs
- Añadir firma digital
- Crear sistema de versiones de documentos
