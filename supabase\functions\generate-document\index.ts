import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import JSZip from 'jszip'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DocumentData {
  Nombre1?: string;
  PrimerApellido1?: string;
  SegundoApellido1?: string;
  PASAPORTE1?: string;
  NIE1?: string;
  Nacionalidad1?: string;
  Localidad1?: string;
  DomicilioEspana1?: string;
  Numero1?: string;
  Bl1?: string;
  Piso1?: string;
  CP1?: string;
  Provincia1?: string;
  TIPOAUTORIZACION?: string;
  FECHA3?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the record ID from the request
    const { recordId } = await req.json()
    
    if (!recordId) {
      throw new Error('Record ID is required')
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch the record data
    console.log('Fetching record with ID:', recordId);

    // Use the correct primary key with proper escaping for column names with dots
    const filterString = `"N. GRAL".eq."${recordId}"`;
    const { data: recordData, error: fetchError } = await supabase
      .from('Exp_Advos')
      .select('*')
      .or(filterString)
      .single()

    if (fetchError) {
      throw new Error(`Error fetching record: ${fetchError.message}`)
    }

    // Prepare data for Gemini
    const documentData: DocumentData = {
      Nombre1: recordData['Nombre1'],
      PrimerApellido1: recordData['1er Apellido1'],
      SegundoApellido1: recordData['2º Apellido1'],
      PASAPORTE1: recordData['PASAPORTE1'],
      NIE1: recordData['N.I.E1'],
      Nacionalidad1: recordData['Nacionalidad1'],
      Localidad1: recordData['Localidad1'],
      DomicilioEspana1: recordData['Domicilio en España1'],
      Numero1: recordData['Nº1'],
      Bl1: recordData['Bl1'],
      Piso1: recordData['Piso1'],
      CP1: recordData['C.P.1'],
      Provincia1: recordData['Provincia1'],
      TIPOAUTORIZACION: recordData['TIPO AUTORIZACIÓN'],
      FECHA3: recordData['FECHA3']
    }

    // Download original document from storage (as binary buffer)
    const templateBuffer = await downloadOriginalDocumentAsBuffer(supabase);

    // Replace placeholders in the DOCX template
    const completedDocument = await replacePlaceholdersInDocx(templateBuffer, documentData);

    // Upload to Supabase Storage as DOCX file
    const fileName = `Representacion_${documentData.Nombre1 || 'SinNombre'}_${documentData.PrimerApellido1 || ''}_${Date.now()}.docx`

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('DOCUMENTOS')
      .upload(`GENERATED/${fileName}`, completedDocument, {
        contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })

    if (uploadError) {
      throw new Error(`Error uploading document: ${uploadError.message}`)
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('DOCUMENTOS')
      .getPublicUrl(`GENERATED/${fileName}`)

    return new Response(
      JSON.stringify({
        success: true,
        downloadUrl: urlData.publicUrl,
        fileName: fileName
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})

async function downloadOriginalDocument(supabase: any): Promise<string> {
  try {
    const { data, error } = await supabase.storage
      .from('DOCUMENTOS')
      .download('REPRESENTACION/REPRESENTACION ADVOS.docx');

    if (error) {
      throw new Error(`Error downloading template: ${error.message}`);
    }

    // For now, return a basic template text since we can't easily extract from .docx in Deno
    // In production, you'd use a proper docx extraction library
    return `OTORGAMIENTO DE LA REPRESENTACIÓN.
[NOMBRE_COMPLETO], con número de pasaporte [PASAPORTE] / NIE [NIE], nacional de [NACIONALIDAD], con domicilio en [LOCALIDAD] [DOMICILIO] [NUMERO] [BL] [PISO] con código postal [CP] provincia [PROVINCIA] OTORGA SU REPRESENTACIÓN a:
Dª Matilde Mérida Rodríguez D.N.I. 30526079L, Letrada del Colegio de Abogados de Córdoba, col núm, 2.410, con domicilio a efectos de notificaciones en su despacho profesional, sito en la calle Alcalde Sanz Noguer, 40, 1º-3 de Córdoba, para que actúe ante:
1. SUBDELEGACIÓN DEL GOBIERNO EN CÓRDOBA, y sus organismos dependientes incluida la OFICINA DE EXTRANJERÍA en la solicitud de [TIPO_AUTORIZACION]
Con relación a dicho procedimiento podrá ejercitar las siguientes facultades: Iniciar el expediente mediante solicitud telemática, facilitar la práctica de cuantas actuaciones sean precisas para la instrucción del expediente, aportar cuantos datos y documentos se soliciten o se interesen, recibir todo tipo de comunicaciones, formular peticiones y solicitudes, presentar escritos y alegaciones, manifestar su decisión de no efectuar alegaciones ni aportar nuevos documentos en el correspondiente trámite de audiencia o renunciar a otros derechos, suscribir diligencias y otros documentos que pueda extender el órgano competente, pagar tasas en su nombre y, en general, realizar cuantas actuaciones correspondan al representado en el curso de dicho procedimiento. Incluido el desistimiento, y la presentación de recurso administrativo de cualquier clase.
2. EL MINISTERIO DE HACIENDA Y ADMINISTRACIONES PÚBLICAS, con el objeto de: pagar tasa, presentar la misma en el expediente sobre [TIPO_AUTORIZACION]
ACEPTACIÓN DE LA REPRESENTACIÓN
Con la firma del presente escrito el representante acepta la representación conferida y responde de la autenticidad de la firma del otorgante.
En Córdoba, a [FECHA]
EL/LA OTORGANTE    LA REPRESENTANTE
[NOMBRE_COMPLETO]    MATILDE MÉRIDA RODRÍGUEZ`;
  } catch (error) {
    console.error('Error downloading original document:', error);
    throw error;
  }
}

async function generateDocumentWithGemini(data: DocumentData, originalTemplate: string): Promise<string> {
  const geminiApiKey = Deno.env.get('GEMINI_API_KEY')

  if (!geminiApiKey || geminiApiKey === 'YOUR_GEMINI_API_KEY_HERE') {
    throw new Error('Gemini API key not configured')
  }

  const prompt = `
Tienes este documento legal oficial como plantilla base:

"${originalTemplate}"

Adapta ÚNICAMENTE los datos personales con esta información específica:
- Nombre completo: ${data.Nombre1} ${data.PrimerApellido1} ${data.SegundoApellido1}
- Pasaporte: ${data.PASAPORTE1}
- NIE: ${data.NIE1}
- Nacionalidad: ${data.Nacionalidad1}
- Domicilio: ${data.DomicilioEspana1} ${data.Numero1} ${data.Bl1} ${data.Piso1}, ${data.Localidad1}, ${data.CP1}, ${data.Provincia1}
- Tipo de autorización: ${data.TIPOAUTORIZACION}
- Fecha: ${data.FECHA3}

IMPORTANTE:
- Mantén EXACTAMENTE la misma estructura legal del documento original
- NO cambies el contenido jurídico
- Solo personaliza los datos del cliente reemplazando los placeholders
- Conserva todo el formato profesional
- Responde SOLO con el texto del documento adaptado, sin explicaciones adicionales

Reemplaza todos los placeholders [NOMBRE_COMPLETO], [PASAPORTE], [NIE], etc. con los datos proporcionados.
`

  const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${geminiApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contents: [{
        parts: [{
          text: prompt
        }]
      }]
    })
  })

  if (!response.ok) {
    throw new Error(`Gemini API error: ${response.statusText}`)
  }

  const result = await response.json()
  return result.candidates[0].content.parts[0].text
}

// New function to download the template as binary buffer
async function downloadOriginalDocumentAsBuffer(supabase: any): Promise<Uint8Array> {
  try {
    const { data, error } = await supabase.storage
      .from('DOCUMENTOS')
      .download('REPRESENTACION/REPRESENTACION ADVOS.docx');

    if (error) {
      throw new Error(`Error downloading template: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data received from storage');
    }

    // Convert blob to ArrayBuffer and then to Uint8Array
    const arrayBuffer = await data.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  } catch (error) {
    console.error('Error in downloadOriginalDocumentAsBuffer:', error);
    throw error;
  }
}

// New function to replace placeholders in DOCX using JSZip
async function replacePlaceholdersInDocx(templateBuffer: Uint8Array, data: DocumentData): Promise<Uint8Array> {
  try {
    // Load the DOCX file as a ZIP archive
    const zip = await JSZip.loadAsync(templateBuffer);

    // Get the main document content (word/document.xml)
    const documentXml = await zip.file('word/document.xml')?.async('string');

    if (!documentXml) {
      throw new Error('Could not find document.xml in DOCX file');
    }

    // Replace all placeholders in the XML content
    let modifiedXml = documentXml;
    modifiedXml = modifiedXml.replace(/\{\{Nombre1\}\}/g, data.Nombre1 || '');
    modifiedXml = modifiedXml.replace(/\{\{PrimerApellido1\}\}/g, data.PrimerApellido1 || '');
    modifiedXml = modifiedXml.replace(/\{\{SegundoApellido1\}\}/g, data.SegundoApellido1 || '');
    modifiedXml = modifiedXml.replace(/\{\{PASAPORTE1\}\}/g, data.PASAPORTE1 || '');
    modifiedXml = modifiedXml.replace(/\{\{NIE1\}\}/g, data.NIE1 || '');
    modifiedXml = modifiedXml.replace(/\{\{Nacionalidad1\}\}/g, data.Nacionalidad1 || '');
    modifiedXml = modifiedXml.replace(/\{\{Localidad1\}\}/g, data.Localidad1 || '');
    modifiedXml = modifiedXml.replace(/\{\{DomicilioEspana1\}\}/g, data.DomicilioEspana1 || '');
    modifiedXml = modifiedXml.replace(/\{\{Numero1\}\}/g, data.Numero1 || '');
    modifiedXml = modifiedXml.replace(/\{\{Bl1\}\}/g, data.Bl1 || '');
    modifiedXml = modifiedXml.replace(/\{\{Piso1\}\}/g, data.Piso1 || '');
    modifiedXml = modifiedXml.replace(/\{\{CP1\}\}/g, data.CP1 || '');
    modifiedXml = modifiedXml.replace(/\{\{Provincia1\}\}/g, data.Provincia1 || '');
    modifiedXml = modifiedXml.replace(/\{\{TIPOAUTORIZACION\}\}/g, data.TIPOAUTORIZACION || '');
    modifiedXml = modifiedXml.replace(/\{\{FECHA3\}\}/g, data.FECHA3 || '');

    // Update the document.xml in the ZIP
    zip.file('word/document.xml', modifiedXml);

    // Generate the modified DOCX file
    const modifiedDocx = await zip.generateAsync({ type: 'uint8array' });

    return modifiedDocx;
  } catch (error) {
    console.error('Error replacing placeholders in DOCX:', error);
    throw error;
  }
}
