import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    // Download the PDF template from storage
    const { data, error } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .download('EX/EX02.pdf')

    if (error) {
      throw new Error(`Error downloading PDF: ${error.message}`)
    }

    // Load the PDF
    const pdfBytes = await data.arrayBuffer()
    const pdfDoc = await PDFDocument.load(pdfBytes)

    // Get the form
    const form = pdfDoc.getForm()
    const fields = form.getFields()

    // Collect field information
    const fieldInfo = fields.map(field => {
      const fieldName = field.getName()
      const fieldType = field.constructor.name
      
      return {
        name: fieldName,
        type: fieldType
      }
    })

    return new Response(
      JSON.stringify({
        success: true,
        totalFields: fields.length,
        fields: fieldInfo
      }, null, 2),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message,
        stack: error.stack
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

