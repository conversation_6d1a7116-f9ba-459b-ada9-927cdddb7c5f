import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    console.log('Downloading PDF template...')
    const { data, error } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .download('EX/EX02.pdf')

    if (error) {
      throw new Error(`Error downloading PDF: ${error.message}`)
    }

    console.log('Loading PDF document...')
    const pdfBytes = await data.arrayBuffer()
    const pdfDoc = await PDFDocument.load(pdfBytes)
    const form = pdfDoc.getForm()

    // Get all fields
    const fields = form.getFields()
    const fieldMapping: any = {
      textFields: [],
      checkboxes: [],
      radioButtons: [],
      dropdowns: []
    }

    fields.forEach((field) => {
      const fieldName = field.getName()
      const fieldType = field.constructor.name

      const fieldInfo = {
        name: fieldName,
        type: fieldType
      }

      if (fieldType === 'PDFTextField') {
        fieldMapping.textFields.push(fieldInfo)
      } else if (fieldType === 'PDFCheckBox') {
        fieldMapping.checkboxes.push(fieldInfo)
      } else if (fieldType === 'PDFRadioGroup') {
        fieldMapping.radioButtons.push(fieldInfo)
      } else if (fieldType === 'PDFDropdown') {
        fieldMapping.dropdowns.push(fieldInfo)
      }
    })

    return new Response(
      JSON.stringify({
        success: true,
        totalFields: fields.length,
        mapping: fieldMapping
      }, null, 2),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

