// document-generator.js - Lógica para generar documentos .docx a partir de plantillas.

/**
 * Carga un archivo desde una URL y devuelve su contenido binario.
 * Esta función es un "helper" para Docxtemplater.
 * @param {string} url - La URL del archivo a cargar.
 * @param {function} callback - Función a ejecutar tras la carga (recibe error y contenido).
 */
function loadFile(url, callback) {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'arraybuffer'; // Es importante para manejar archivos binarios como .docx

    xhr.onload = function (e) {
        if (this.status === 200) {
            callback(null, this.response);
        } else {
            // Si hay un error (ej. 404 Not Found), lo pasamos al callback
            callback(new Error(`Error al cargar la plantilla: ${this.statusText}`), null);
        }
    };

    xhr.onerror = function () {
        // Maneja errores de red
        callback(new Error("Error de red al intentar cargar la plantilla."), null);
    };

    xhr.send();
}

/**
 * Función principal para generar y descargar un documento .docx usando Edge Function.
 * @param {string} id - El ID del registro.
 * @param {string} tableName - El nombre de la tabla (Exp_Advos, Nacionalidad, etc.).
 * @param {string} documentType - El tipo de documento (para tablas con múltiples plantillas).
 */
async function handleGenerateDocument(id, tableName = 'Exp_Advos', documentType = null) {
    if (!id) {
        alert('Error: Identificador de registro inválido.');
        return;
    }

    // Determinar qué Edge Function llamar según la tabla y tipo de documento
    let functionName;

    if (tableName === 'Exp_Advos') {
        if (documentType === 'representacion') {
            functionName = 'generate-document';
        } else if (documentType === 'ex10') {
            functionName = 'generate-pdf-ex10';
        } else if (documentType === 'ex02') {
            functionName = 'generate-pdf-ex02';
        } else if (documentType === 'ex03') {
            functionName = 'generate-pdf-ex03';
        } else {
            alert('Error: Debe seleccionar un tipo de documento.');
            return;
        }
    } else if (tableName === 'Nacionalidad') {
        if (documentType === 'nacionalidad') {
            functionName = 'generate-document-nacionalidad';
        } else if (documentType === 'oar') {
            functionName = 'generate-document-oar';
        } else {
            alert('Error: Debe seleccionar un tipo de documento.');
            return;
        }
    }

    if (!functionName) {
        alert(`Error: No hay función de generación de documentos para la tabla ${tableName}`);
        return;
    }

    // Muestra un mensaje temporal al usuario
    const statusMessage = document.createElement('div');
    statusMessage.textContent = 'Generando documento... Por favor, espere.';
    statusMessage.style.cssText = `
        position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
        background-color: #3498db; color: white; padding: 15px;
        border-radius: 5px; z-index: 2000; box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    `;
    document.body.appendChild(statusMessage);

    try {
        // Llamar a la Edge Function correspondiente
        const response = await fetch(`https://twxtlrjbpxifcdzepxfk.supabase.co/functions/v1/${functionName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}` // Usar la clave anónima definida en script.js
            },
            body: JSON.stringify({
                recordId: id
            })
        });

        if (!response.ok) {
            throw new Error(`Error en la Edge Function: ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error || 'Error desconocido en la generación del documento');
        }

        // Actualizar mensaje de estado
        statusMessage.textContent = 'Descargando documento...';

        // Descargar el archivo generado
        const downloadResponse = await fetch(result.downloadUrl);
        if (!downloadResponse.ok) {
            throw new Error('Error al descargar el documento generado');
        }

        const blob = await downloadResponse.blob();

        // Crear un enlace de descarga
        const downloadLink = document.createElement('a');
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = result.fileName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Limpiar la URL del objeto
        URL.revokeObjectURL(downloadLink.href);

        // Elimina el mensaje de estado
        document.body.removeChild(statusMessage);

        // Mostrar mensaje de éxito
        alert('Documento generado y descargado exitosamente!');

    } catch (error) {
        console.error('Error generando el documento:', error);
        alert(`No se pudo generar el documento: ${error.message}`);
        if (document.body.contains(statusMessage)) {
            document.body.removeChild(statusMessage);
        }
    }
}