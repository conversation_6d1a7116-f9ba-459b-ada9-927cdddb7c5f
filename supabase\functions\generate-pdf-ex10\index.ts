import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { recordId } = await req.json()

    if (!recordId) {
      throw new Error('Record ID is required')
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch the record data
    console.log('Fetching record with N. GRAL:', recordId);

    const filterString = `"N. GRAL".eq."${recordId}"`;
    const { data: recordData, error: fetchError } = await supabase
      .from('Exp_Advos')
      .select('*')
      .or(filterString)
      .single()

    if (fetchError) {
      throw new Error(`Error fetching record: ${fetchError.message}`)
    }

    // Download the PDF template
    const { data: pdfData, error: downloadError } = await supabase.storage
      .from('DOCUMENTOS')
      .download('EX/EX10.pdf')

    if (downloadError) {
      throw new Error(`Error downloading PDF: ${downloadError.message}`)
    }

    // Convert blob to ArrayBuffer
    const arrayBuffer = await pdfData.arrayBuffer()
    const pdfBytes = new Uint8Array(arrayBuffer)

    // Load and fill the PDF
    const pdfDoc = await PDFDocument.load(pdfBytes)
    const form = pdfDoc.getForm()

    // Fill the form fields based on the mapping
    fillFormFields(form, recordData)

    // NO aplanar el formulario para mantenerlo editable
    // form.flatten()

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save()

    // Upload to Supabase Storage
    const fileName = `EX10_${recordData['Nombre1'] || 'SinNombre'}_${recordData['1er Apellido1'] || ''}_${Date.now()}.pdf`

    const { error: uploadError } = await supabase.storage
      .from('DOCUMENTOS')
      .upload(`GENERATED/${fileName}`, filledPdfBytes, {
        contentType: 'application/pdf'
      })

    if (uploadError) {
      throw new Error(`Error uploading PDF: ${uploadError.message}`)
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('DOCUMENTOS')
      .getPublicUrl(`GENERATED/${fileName}`)

    return new Response(
      JSON.stringify({
        success: true,
        downloadUrl: urlData.publicUrl,
        fileName: fileName
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

function fillFormFields(form: any, data: any) {
  // Helper function to safely set text field
  const setText = (fieldName: string, value: any) => {
    try {
      const field = form.getTextField(fieldName)
      if (field && value) {
        field.setText(String(value))
      }
    } catch (e) {
      console.log(`Field ${fieldName} not found or error:`, e.message)
    }
  }

  // Helper function to safely set checkbox
  const setCheckbox = (fieldName: string, checked: boolean) => {
    try {
      const field = form.getCheckBox(fieldName)
      if (field) {
        if (checked) {
          field.check()
        } else {
          field.uncheck()
        }
      }
    } catch (e) {
      console.log(`Checkbox ${fieldName} not found or error:`, e.message)
    }
  }

  // SECCIÓN 1: DATOS DE LA PERSONA EXTRANJERA
  setText('Texto1', data['PASAPORTE1'])

  // NIE completo en el campo grande (Texto3), campos pequeños vacíos (Texto2 y Texto4)
  setText('Texto3', data['N.I.E1'])
  // Texto2 y Texto4 quedan vacíos

  setText('Texto5', data['1er Apellido1'])
  setText('Texto6', data['2º Apellido1'])
  setText('Texto7', data['Nombre1'])

  // Sexo - Casillas 2, 3, 4 (X, H, M)
  const sexo1 = data['Sexo1']?.toUpperCase()
  setCheckbox('Casilla de verificación2', sexo1 === 'X')
  setCheckbox('Casilla de verificación3', sexo1 === 'H')
  setCheckbox('Casilla de verificación4', sexo1 === 'M')

  // Fecha de nacimiento (formato DD/MM/YYYY)
  if (data['F nac1']) {
    const fechaStr = data['F nac1']
    const partes = fechaStr.split('/')
    if (partes.length === 3) {
      setText('Texto8', partes[0].padStart(2, '0'))   // Día
      setText('Texto9', partes[1].padStart(2, '0'))   // Mes
      setText('Texto10', partes[2])                   // Año
    }
  }

  setText('Texto11', data['Lugar1'])
  setText('Texto12', data['País1'])
  setText('Texto13', data['Nacionalidad1'])

  // Estado civil - Casillas 5, 6, 7, 8, 9 (S, C, V, D, Separado)
  const estadoCivil = data['Estado civil1']?.toUpperCase()
  setCheckbox('Casilla de verificación5', estadoCivil === 'S')
  setCheckbox('Casilla de verificación6', estadoCivil === 'C')
  setCheckbox('Casilla de verificación7', estadoCivil === 'V')
  setCheckbox('Casilla de verificación8', estadoCivil === 'D')
  setCheckbox('Casilla de verificación9', estadoCivil === 'SEPARADO' || estadoCivil === 'SEP')

  setText('Texto14', data['Padre1'])
  setText('Texto15', data['Madre1'])
  setText('Texto16', data['Domicilio en España1'])
  setText('Texto17', data['Nº1'])
  setText('Texto18', data['Piso1'])
  setText('Texto19', data['Localidad1'])
  setText('Texto20', data['C.P.1'])
  setText('Texto21', data['Provincia1'])
  setText('Texto22', data['Teléfono1'])
  setText('Texto23', data['E-mail1'])
  // Texto24, 25, 26 no se rellenan (representante legal)

  // SECCIÓN 2: DATOS DEL FAMILIAR CIUDADANO DE LA UE/EEE/SUIZA
  setText('Texto27', data['PASAPORTE2'])

  // NIE2 completo en el campo grande (Texto29), campos pequeños vacíos (Texto28 y Texto30)
  setText('Texto29', data['D.N.I./NIE2'])
  // Texto28 y Texto30 quedan vacíos

  setText('Texto31', data['1er Apellido2'])
  setText('Texto32', data['2º Apellido2'])
  setText('Texto33', data['Nombre2'])

  // Sexo2 - Casillas 11, 12, 13 (X, H, M)
  const sexo2 = data['Sexo2']?.toUpperCase()
  setCheckbox('Casilla de verificación11', sexo2 === 'X')
  setCheckbox('Casilla de verificación12', sexo2 === 'H')
  setCheckbox('Casilla de verificación13', sexo2 === 'M')

  // Fecha nacimiento 2 (formato DD/MM/YYYY)
  if (data['F nac2']) {
    const fechaStr2 = data['F nac2']
    const partes2 = fechaStr2.split('/')
    if (partes2.length === 3) {
      setText('Texto34', partes2[0].padStart(2, '0'))  // Día
      setText('Texto35', partes2[1].padStart(2, '0'))  // Mes
      setText('Texto36', partes2[2])                   // Año
    }
  }

  setText('Texto37', data['País2'])
  setText('Texto38', data['Padre2'])
  setText('Texto39', data['Madre2'])
  setText('Texto40', data['Domicilio en España2'])
  setText('Texto41', data['Nº2'])
  setText('Texto42', data['Piso2'])
  setText('Texto43', data['Localidad2'])
  setText('Texto44', data['C.P.2'])
  setText('Texto45', data['Provincia2'])
  setText('Texto46', data['Vínculo2'])

  // SECCIÓN 3: DATOS DEL REPRESENTANTE (siempre los mismos datos)
  setText('Texto47', 'MATILDE MÉRIDA RODRIGUEZ')
  setText('Texto48', '30526079L')
  setText('Texto49', 'CALLE ALCALDE SANZ NOGUER')
  setText('Texto50', '3')
  setText('Texto51', '1º3')
  setText('Texto52', 'CORDOBA')
  setText('Texto53', '14005')
  setText('Texto54', 'CORDOBA')
  setText('Texto55', '661754888')
  setText('Texto56', '<EMAIL>')
  // Texto57, 58, 59 no se rellenan

  // SECCIÓN 4: DOMICILIO A EFECTOS DE NOTIFICACIONES
  // Texto60 a Texto69 no se rellenan

  // SECCIÓN 5: DATOS DEL EMPLEADOR/A
  setText('Texto70', data['Nombre/Razón Socia3'])
  setText('Texto71', data['NIF/CIF3'])
  setText('Texto72', data['Actividad3'])
  // Texto73 (CNAE) y Texto74 (CNO) no se rellenan
  setText('Texto75', data['Domicilio3'])
  // Texto76 y Texto77 parecen ser Nº y Piso pero no hay correspondencia
  setText('Texto78', data['Localidad3'])
  setText('Texto79', data['C.P.3'])
  setText('Texto80', data['Provincia3'])
  setText('Texto81', data['Teléfono3'])
  setText('Texto82', data['E-mail3'])
  setText('Texto83', data['Representante3'])
  setText('Texto84', data['DNI/NIE/PAS3'])
  setText('Texto85', data['Título3'])

  // SECCIÓN 6: CENTRO DE FORMACIÓN
  // Texto82 a Texto90 y casillas 24-26 no se rellenan

  // SECCIÓN 7: TIPO DE AUTORIZACIÓN SOLICITADA
  // Mapeo de tipos de autorización a casillas de verificación
  const tipoAutorizacion = data['TIPO AUTORIZACIÓN']?.toLowerCase() || ''

  // Casillas 27-53 según el tipo de autorización
  // Este mapeo puede necesitar ajustes según los valores exactos en tu base de datos
  setCheckbox('Casilla de verificación27', tipoAutorizacion.includes('residencia inicial'))
  setCheckbox('Casilla de verificación28', tipoAutorizacion.includes('prórroga'))
  setCheckbox('Casilla de verificación29', tipoAutorizacion.includes('provisional'))
  setCheckbox('Casilla de verificación30', tipoAutorizacion.includes('arraigo segunda oportunidad'))
  setCheckbox('Casilla de verificación31', tipoAutorizacion.includes('arraigo sociolaboral'))
  setCheckbox('Casilla de verificación32', tipoAutorizacion.includes('arraigo social'))
  setCheckbox('Casilla de verificación33', tipoAutorizacion.includes('arraigo socioformativo'))
  setCheckbox('Casilla de verificación34', tipoAutorizacion.includes('arraigo familiar'))
  setCheckbox('Casilla de verificación35', tipoAutorizacion.includes('razones humanitarias') && tipoAutorizacion.includes('víctimas'))
  setCheckbox('Casilla de verificación36', tipoAutorizacion.includes('razones humanitarias') && tipoAutorizacion.includes('enfermedad'))
  setCheckbox('Casilla de verificación37', tipoAutorizacion.includes('razones humanitarias') && tipoAutorizacion.includes('progenitor'))
  setCheckbox('Casilla de verificación38', tipoAutorizacion.includes('razones humanitarias') && tipoAutorizacion.includes('peligro'))
  setCheckbox('Casilla de verificación39', tipoAutorizacion.includes('colaboración'))
  setCheckbox('Casilla de verificación40', tipoAutorizacion.includes('razones de interés público'))
  setCheckbox('Casilla de verificación41', tipoAutorizacion.includes('mujer en situación de violencia'))
  setCheckbox('Casilla de verificación42', tipoAutorizacion.includes('hijo menor'))
  setCheckbox('Casilla de verificación43', tipoAutorizacion.includes('padecimiento de víctima'))
  setCheckbox('Casilla de verificación44', tipoAutorizacion.includes('víctimas de violencia sexual'))
  setCheckbox('Casilla de verificación45', tipoAutorizacion.includes('hijo menor o discapacitado'))
  setCheckbox('Casilla de verificación46', tipoAutorizacion.includes('adulto responsable'))
  setCheckbox('Casilla de verificación47', tipoAutorizacion.includes('colaboración contra redes'))
  setCheckbox('Casilla de verificación48', tipoAutorizacion.includes('víctima de la trata'))
  setCheckbox('Casilla de verificación49', tipoAutorizacion.includes('hijo menor o discapacitado, tutelado'))
  setCheckbox('Casilla de verificación50', tipoAutorizacion.includes('circunstancias excepcionales'))
}

