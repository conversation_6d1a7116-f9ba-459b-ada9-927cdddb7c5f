import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { recordId } = await req.json()

    if (!recordId) {
      throw new Error('recordId is required')
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    console.log('Downloading PDF template...')
    // Download the PDF template from storage
    const { data, error } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .download('EX/EX02.pdf')

    if (error) {
      console.error('Error downloading PDF:', error)
      throw new Error(`Error downloading PDF: ${error.message}`)
    }

    console.log('Loading PDF document...')
    // Load the PDF
    const pdfBytes = await data.arrayBuffer()
    const pdfDoc = await PDFDocument.load(pdfBytes)

    // Get the form
    const form = pdfDoc.getForm()
    console.log('PDF form loaded successfully')

    console.log('Fetching record data for N. GRAL:', recordId)
    // Fetch the record data from Exp_Advos table
    // Use the correct primary key with proper escaping for column names with dots
    const filterString = `"N. GRAL".eq."${recordId}"`
    const { data: recordData, error: fetchError } = await supabaseClient
      .from('Exp_Advos')
      .select('*')
      .or(filterString)
      .single()

    if (fetchError) {
      console.error('Error fetching record:', fetchError)
      throw new Error(`Error fetching record: ${fetchError.message}`)
    }

    if (!recordData) {
      throw new Error(`No record found with N. GRAL: ${recordId}`)
    }

    console.log('Record data fetched successfully')
    // Fill the form fields based on the mapping
    fillFormFields(form, recordData)
    console.log('Form fields filled successfully')

    // NO aplanar el formulario para mantenerlo editable
    // form.flatten()

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save()

    // Generate filename
    const fileName = `EX02_${recordData['Nombre1']}_${recordData['1er Apellido1']}_${new Date().toISOString().split('T')[0]}.pdf`

    // Upload to storage
    const { error: uploadError } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .upload(`generated/${fileName}`, filledPdfBytes, {
        contentType: 'application/pdf',
        upsert: true
      })

    if (uploadError) {
      throw new Error(`Error uploading PDF: ${uploadError.message}`)
    }

    // Get public URL
    const { data: urlData } = supabaseClient.storage
      .from('DOCUMENTOS')
      .getPublicUrl(`generated/${fileName}`)

    return new Response(
      JSON.stringify({
        success: true,
        downloadUrl: urlData.publicUrl,
        fileName: fileName
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error in generate-pdf-ex02:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

function fillFormFields(form: any, data: any) {
  // Helper function to safely set text field
  const setText = (fieldName: string, value: any) => {
    try {
      const field = form.getTextField(fieldName)
      if (field && value) {
        field.setText(String(value))
      }
    } catch (e) {
      const errorMsg = e instanceof Error ? e.message : 'Unknown error'
      console.log(`Field ${fieldName} not found or error:`, errorMsg)
    }
  }

  // Helper function to safely set checkbox
  const setCheckbox = (fieldName: string, checked: boolean) => {
    try {
      const field = form.getCheckBox(fieldName)
      if (field) {
        if (checked) {
          field.check()
        } else {
          field.uncheck()
        }
      }
    } catch (e) {
      const errorMsg = e instanceof Error ? e.message : 'Unknown error'
      console.log(`Checkbox ${fieldName} not found or error:`, errorMsg)
    }
  }

  // SECCIÓN 1: DATOS DE LA PERSONA EXTRANJERA REAGRUPANTE
  setText('Texto1', data['PASAPORTE1'])

  // NIE completo en el campo grande (Texto3), campos pequeños vacíos (Texto2 y Texto4)
  setText('Texto3', data['N.I.E1'])
  // Texto2 y Texto4 quedan vacíos

  setText('Texto5', data['1er Apellido1'])
  setText('Texto6', data['2º Apellido1'])
  setText('Texto7', data['Nombre1'])

  // Sexo - Casillas 2, 3 (H, M)
  const sexo1 = data['Sexo1']?.toUpperCase()
  setCheckbox('Casilla de verificación2', sexo1 === 'H')
  setCheckbox('Casilla de verificación3', sexo1 === 'M')

  // Fecha de nacimiento
  if (data['F nac1']) {
    const fecha = new Date(data['F nac1'])
    setText('Texto8', fecha.getDate().toString().padStart(2, '0'))
    setText('Texto9', (fecha.getMonth() + 1).toString().padStart(2, '0'))
    setText('Texto10', fecha.getFullYear().toString())
  }

  setText('Texto11', data['Lugar1'])
  setText('Texto12', data['País1'])
  setText('Texto13', data['Nacionalidad1'])

  // Estado civil - Casillas 5-9
  const estadoCivil = data['Estado civil1']?.toUpperCase()
  setCheckbox('Casilla de verificación5', estadoCivil === 'S')
  setCheckbox('Casilla de verificación6', estadoCivil === 'C')
  setCheckbox('Casilla de verificación7', estadoCivil === 'V')
  setCheckbox('Casilla de verificación8', estadoCivil === 'D')
  setCheckbox('Casilla de verificación9', estadoCivil === 'SEPARADO' || estadoCivil === 'SEP')

  setText('Texto14', data['Padre1'])
  setText('Texto15', data['Madre1'])
  setText('Texto16', data['Domicilio en España1'])
  setText('Texto17', data['Nº1'])
  setText('Texto18', data['Piso1'])
  setText('Texto19', data['Localidad1'])
  setText('Texto20', data['C.P.1'])
  setText('Texto21', data['Provincia1'])
  setText('Texto22', data['Teléfono1'])
  setText('Texto23', data['E-mail1'])

  // Autorización de la que es titular
  setText('Texto24', data['TIPO AUTORIZACIÓN'])
  // Texto25 (DNI/NIE/PAS) y Texto26 (Título) no se rellenan

  // Checkboxes SI/NO para "Hijas/os a cargo en edad de escolarización en España"
  // Casilla de verificación1 = SI, Casilla de verificación4 = NO
  const tieneHijos = data['Escolariz2']?.toUpperCase() === 'SI'
  setCheckbox('Casilla de verificación1', tieneHijos)
  setCheckbox('Casilla de verificación4', !tieneHijos)

  // Representante legal, en su caso (Sección 1) - NO SE RELLENA
  // Texto27, Texto28, Texto29 (o similar) quedan vacíos

  // SECCIÓN 1) DATOS DE LA PERSONA EXTRANJERA REAGRUPADA O QUE SE PRETENDE REAGRUPAR
  // NOTA: Texto27-29 son para "Representante legal" de Sección 1, NO se rellenan
  // Empezamos desde Texto30 o posterior para PASAPORTE2

  setText('Texto31', data['PASAPORTE2'])

  // NIE2 completo en el campo grande
  setText('Texto33', data['D.N.I./NIE2'])

  setText('Texto34', data['1er Apellido2'])
  setText('Texto35', data['2º Apellido2'])
  setText('Texto36', data['Nombre2'])

  // Sexo2 - Casillas 11, 12 (H, M)
  const sexo2 = data['Sexo2']?.toUpperCase()
  setCheckbox('Casilla de verificación11', sexo2 === 'H')
  setCheckbox('Casilla de verificación12', sexo2 === 'M')

  // Fecha nacimiento 2
  if (data['F nac2']) {
    const fecha2 = new Date(data['F nac2'])
    setText('Texto37', fecha2.getDate().toString().padStart(2, '0'))
    setText('Texto38', (fecha2.getMonth() + 1).toString().padStart(2, '0'))
    setText('Texto39', fecha2.getFullYear().toString())
  }
  setText('Texto40', data['Lugar2'])
  setText('Texto41', data['Nacionalidad2'])
  setText('Texto42', data['Nacionalidad2'])

  // Estado civil 2 - Casillas 14-18
  const estadoCivil2 = data['Estado civil2']?.toUpperCase()
  setCheckbox('Casilla de verificación14', estadoCivil2 === 'S')
  setCheckbox('Casilla de verificación15', estadoCivil2 === 'C')
  setCheckbox('Casilla de verificación16', estadoCivil2 === 'V')
  setCheckbox('Casilla de verificación17', estadoCivil2 === 'D')
  setCheckbox('Casilla de verificación18', estadoCivil2 === 'SEPARADO' || estadoCivil2 === 'SEP')

  setText('Texto43', data['Padre2'])
  setText('Texto34', data['Madre2'])
  setText('Texto45', data['Domicilio en España2'])
  setText('Texto46', data['Nº2'])
  setText('Texto47', data['Piso2'])
  setText('Texto48', data['Localidad2'])
  setText('Texto49', data['C.P.2'])
  setText('Texto50', data['Provincia2'])

  // SECCIÓN 2) DATOS DEL REPRESENTANTE A EFECTOS DE PRESENTACIÓN DE LA SOLICITUD
  setText('Texto51', 'MATILDE MÉRIDA RODRIGUEZ')
  setText('Texto52', '30526079L')
  setText('Texto53', 'CALLE ALCALDE SANZ NOGUER')
  setText('Texto54', '3')
  setText('Texto55', '1º3')
  setText('Texto56', 'CORDOBA')
  setText('Texto57', '14005')
  setText('Texto58', 'CORDOBA')
  setText('Texto59', '661754888')
  setText('Texto60', '<EMAIL>')
  // Texto46, Texto57, Texto68, Texto69 (Representante legal) NO se rellenan

  // SECCIÓN 3) DOMICILIO A EFECTOS DE NOTIFICACIONES - NO SE RELLENA
  // Texto58 a Texto67 quedan vacíos
  // Casilla de verificación19 queda sin marcar

  // SECCIÓN 5: TIPO DE AUTORIZACIÓN SOLICITADA
  // Mapeo de tipos de autorización a casillas de verificación
  const tipoAutorizacion = data['TIPO AUTORIZACIÓN']?.toUpperCase() || ''

  // Reagrupación familiar
  if (tipoAutorizacion.includes('REAGRUPACIÓN FAMILIAR') || tipoAutorizacion.includes('REAGRUPACION FAMILIAR')) {
    if (tipoAutorizacion.includes('INICIAL')) {
      setCheckbox('Casilla de verificación20', true) // Reagrupación familiar inicial
    } else if (tipoAutorizacion.includes('RENOVACIÓN') || tipoAutorizacion.includes('RENOVACION')) {
      setCheckbox('Casilla de verificación21', true) // Reagrupación familiar renovación
    }
  }

  // Residencia independiente
  if (tipoAutorizacion.includes('RESIDENCIA INDEPENDIENTE')) {
    setCheckbox('Casilla de verificación22', true)
  }

  // Autorización de trabajo
  if (tipoAutorizacion.includes('TRABAJO')) {
    if (tipoAutorizacion.includes('CUENTA AJENA')) {
      setCheckbox('Casilla de verificación23', true)
    } else if (tipoAutorizacion.includes('CUENTA PROPIA')) {
      setCheckbox('Casilla de verificación24', true)
    }
  }
}

