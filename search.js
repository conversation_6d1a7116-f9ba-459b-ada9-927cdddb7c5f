// search.js - Lógica para la página de búsqueda general

document.addEventListener('DOMContentLoaded', () => {
    const searchForm = document.getElementById('search-form');
    const searchTypeInput = document.getElementById('search-type');
    const searchIdInput = document.getElementById('search-id');
    const searchNameInput = document.getElementById('search-name');
    const clearSearchButton = document.getElementById('clear-search');
    const searchResultsContainer = document.getElementById('search-results');
    const searchLoader = document.getElementById('search-loader');
    const searchMessage = document.getElementById('search-message');

    const tableDisplayNames = { 'Exp_Advos': 'Administrativo', 'Nacionalidad': 'Nacionalidad', 'Proc_Judiciales': 'Judicial', 'Datos_Generales': 'General' };
    const searchableColumns = {
        'Exp_Advos': {
            idFields: ['N.I.E1', 'PASAPORTE1', 'D.N.I./NIE2', 'PASAPORTE2'],
            nameFields: ['Nombre1', '1er Apellido1', '2º Apellido1', 'Nombre2', '1er Apellido2', '2º Apellido2']
        },
        'Nacionalidad': {
            idFields: ['NIE', 'Nº Soporte'],
            nameFields: ['NOMBRE', 'PRIMER APELLIDO', 'SEGUNDO APELLIDO']
        },
        'Proc_Judiciales': {
            idFields: ['NIE/PASAPORTE'],
            nameFields: ['NOMBRE']
        },
        'Datos_Generales': {
            idFields: [],
            nameFields: ['NOMBRE', 'PROCEDIMIENTO', 'COMENTARIO']
        }
    };

    async function handleSearch(event) {
        event.preventDefault();
        const idTerm = searchIdInput.value.trim();
        const nameTerm = searchNameInput.value.trim();
        const typeFilter = searchTypeInput.value;

        if (!idTerm && !nameTerm) {
            searchMessage.textContent = 'Por favor, introduce un término de búsqueda.';
            searchResultsContainer.innerHTML = ''; return;
        }
        searchLoader.classList.remove('hidden');
        searchResultsContainer.innerHTML = '';
        searchMessage.textContent = '';
        try {
            const tablesToSearch = typeFilter === 'Todos' ? Object.keys(searchableColumns) : [typeFilter];
            const searchPromises = tablesToSearch.map(tableName => performSearchInTable(tableName, idTerm, nameTerm));
            const resultsByTable = await Promise.all(searchPromises);
            displayResults(resultsByTable.flat());
        } catch (error) {
            searchMessage.textContent = `Error: ${error.message}`;
        } finally {
            searchLoader.classList.add('hidden');
        }
    }

    async function performSearchInTable(tableName, idTerm, nameTerm) {
        const columns = searchableColumns[tableName];
        let query = supabaseClient.from(tableName).select('*');
        const filters = [];
        if (idTerm && columns.idFields.length > 0) { filters.push(`or(${columns.idFields.map(field => `"${field}".ilike.%${idTerm}%`).join(',')})`); }
        if (nameTerm && columns.nameFields.length > 0) { filters.push(`or(${columns.nameFields.map(field => `"${field}".ilike.%${nameTerm}%`).join(',')})`); }
        if (filters.length === 0) return [];
        query = query.or(filters.join(','));
        const { data, error } = await query;
        if (error) { console.error(`Error buscando en la tabla ${tableName}:`, error); return []; }
        return data.map(item => ({ ...item, sourceType: tableName }));
    }
    
    // ================================================================
    // ===== INICIO: FUNCIÓN displayResults MODIFICADA
    // ================================================================
    function displayResults(results) {
        if (results.length === 0) {
            searchMessage.textContent = 'No se encontraron resultados.'; return;
        }
        results.forEach(item => {
            const card = document.createElement('div');
            card.className = 'result-card';
            const displayName = tableDisplayNames[item.sourceType] || 'Desconocido';

            const primaryKeyName = tablePrimaryKeys[item.sourceType];
            const primaryKeyValue = item[primaryKeyName] || item.id;

            // Crear header con título y botón de editar
            const headerDiv = document.createElement('div');
            headerDiv.className = 'result-card-header';

            const title = document.createElement('h3');
            title.className = 'result-card-title';
            title.textContent = `Tipo: ${displayName} (ID: ${primaryKeyValue})`;

            const editButton = document.createElement('button');
            editButton.className = 'edit-btn-search';
            editButton.textContent = '✏️ Editar';
            editButton.dataset.id = primaryKeyValue;
            editButton.dataset.table = item.sourceType;

            headerDiv.appendChild(title);
            headerDiv.appendChild(editButton);
            card.appendChild(headerDiv);
            
            // Crear fila especial para enlace + botones de generación
            if (item.enlace) {
                const enlaceRow = document.createElement('div');
                enlaceRow.className = 'result-item-enlace';

                const copyButton = document.createElement('button');
                copyButton.className = 'copy-line-button';
                copyButton.innerHTML = '&#128203;';
                copyButton.title = 'Copiar enlace';
                copyButton.dataset.text = item.enlace;

                const keySpan = document.createElement('span');
                keySpan.className = 'result-key';
                keySpan.textContent = 'enlace';

                const linkButton = document.createElement('a');
                linkButton.href = item.enlace;
                linkButton.textContent = '🔗 Ver';
                linkButton.title = 'Ver enlace en nueva pestaña';
                linkButton.target = '_blank';
                linkButton.rel = 'noopener noreferrer';
                linkButton.classList.add('link-button-inline');

                const actionsContainer = document.createElement('div');
                actionsContainer.className = 'enlace-actions';

                // Agregar botones según el tipo de tabla
                if (item.sourceType === 'Exp_Advos') {
                    // Botón de Representación
                    const repBtn = document.createElement('button');
                    repBtn.className = 'generate-btn-inline';
                    repBtn.textContent = 'Representación';
                    repBtn.dataset.id = primaryKeyValue;
                    repBtn.dataset.table = item.sourceType;
                    repBtn.dataset.docType = 'representacion';
                    actionsContainer.appendChild(repBtn);

                    // Desplegable para EX
                    const exSelect = document.createElement('select');
                    exSelect.className = 'generate-select-inline';
                    exSelect.dataset.id = primaryKeyValue;
                    exSelect.dataset.table = item.sourceType;

                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Generar EX...';
                    exSelect.appendChild(defaultOption);

                    const ex10Option = document.createElement('option');
                    ex10Option.value = 'ex10';
                    ex10Option.textContent = 'EX10';
                    exSelect.appendChild(ex10Option);

                    const ex02Option = document.createElement('option');
                    ex02Option.value = 'ex02';
                    ex02Option.textContent = 'EX02';
                    exSelect.appendChild(ex02Option);

                    const ex03Option = document.createElement('option');
                    ex03Option.value = 'ex03';
                    ex03Option.textContent = 'EX03';
                    exSelect.appendChild(ex03Option);

                    actionsContainer.appendChild(exSelect);
                } else if (item.sourceType === 'Nacionalidad') {
                    const nacionalidadBtn = document.createElement('button');
                    nacionalidadBtn.className = 'generate-btn-inline';
                    nacionalidadBtn.textContent = 'Nacionalidad';
                    nacionalidadBtn.dataset.id = primaryKeyValue;
                    nacionalidadBtn.dataset.table = item.sourceType;
                    nacionalidadBtn.dataset.docType = 'nacionalidad';
                    actionsContainer.appendChild(nacionalidadBtn);

                    const oarBtn = document.createElement('button');
                    oarBtn.className = 'generate-btn-inline';
                    oarBtn.textContent = 'OAR';
                    oarBtn.dataset.id = primaryKeyValue;
                    oarBtn.dataset.table = item.sourceType;
                    oarBtn.dataset.docType = 'oar';
                    actionsContainer.appendChild(oarBtn);
                }

                enlaceRow.appendChild(copyButton);
                enlaceRow.appendChild(keySpan);
                enlaceRow.appendChild(linkButton);
                enlaceRow.appendChild(actionsContainer);

                card.appendChild(enlaceRow);
            }

            // Mostrar el resto de campos (excepto enlace)
            for (const key in item) {
                if (key !== 'sourceType' && key !== 'id' && key !== 'enlace' && item[key] !== null && String(item[key]).trim() !== '') {
                    const value = item[key];
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'result-item';

                    const keySpan = document.createElement('span');
                    keySpan.className = 'result-key';
                    keySpan.textContent = key;

                    const valueSpan = document.createElement('span');
                    valueSpan.className = 'result-value';
                    valueSpan.textContent = value;

                    const copyButton = document.createElement('button');
                    copyButton.className = 'copy-line-button';
                    copyButton.innerHTML = '&#128203;';
                    copyButton.title = 'Copiar valor';
                    copyButton.dataset.text = value;

                    itemDiv.appendChild(copyButton);
                    itemDiv.appendChild(keySpan);
                    itemDiv.appendChild(valueSpan);

                    card.appendChild(itemDiv);
                }
            }
            searchResultsContainer.appendChild(card);
        });
    }
    // ================================================================
    // ===== FIN: FUNCIÓN displayResults MODIFICADA
    // ================================================================

    function clearSearch() {
        searchForm.reset();
        searchResultsContainer.innerHTML = '';
        searchMessage.textContent = '';
    }

    function handleCopy(event) {
        if (!event.target.classList.contains('copy-line-button')) return;
        const button = event.target;
        const textToCopy = button.dataset.text;
        navigator.clipboard.writeText(textToCopy).then(() => {
            button.innerHTML = '✅';
            setTimeout(() => { button.innerHTML = '&#128203;'; }, 2000);
        }).catch(err => { alert('No se pudo copiar el texto.'); });
    }

    function handleDocumentGeneration(event) {
        const target = event.target;

        // Manejar desplegable inline (en la fila del enlace)
        if (target.classList.contains('generate-select-inline')) {
            const { id, table } = target.dataset;
            const documentType = target.value;
            if (documentType) {
                handleGenerateDocument(id, table, documentType);
                target.value = ''; // Reset select
            }
        }

        // Manejar botones inline (en la fila del enlace)
        if (target.classList.contains('generate-btn-inline')) {
            const { id, table, docType } = target.dataset;
            handleGenerateDocument(id, table, docType);
        }
    }

    function handleEdit(event) {
        const target = event.target;

        // Manejar botón de editar
        if (target.classList.contains('edit-btn-search')) {
            const { id, table } = target.dataset;
            openEditModal(id, table);
        }
    }

    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
        clearSearchButton.addEventListener('click', clearSearch);
        searchResultsContainer.addEventListener('click', handleCopy);
        searchResultsContainer.addEventListener('change', handleDocumentGeneration);
        searchResultsContainer.addEventListener('click', handleDocumentGeneration);
        searchResultsContainer.addEventListener('click', handleEdit);
    }
});