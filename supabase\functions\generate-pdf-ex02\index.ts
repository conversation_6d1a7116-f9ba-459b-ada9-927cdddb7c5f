import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { recordId } = await req.json()

    if (!recordId) {
      throw new Error('recordId is required')
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    console.log('Downloading PDF template...')
    // Download the PDF template from storage
    const { data, error } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .download('EX/EX02.pdf')

    if (error) {
      console.error('Error downloading PDF:', error)
      throw new Error(`Error downloading PDF: ${error.message}`)
    }

    console.log('Loading PDF document...')
    // Load the PDF
    const pdfBytes = await data.arrayBuffer()
    const pdfDoc = await PDFDocument.load(pdfBytes)

    // Get the form
    const form = pdfDoc.getForm()
    console.log('PDF form loaded successfully')

    console.log('Fetching record data for N. GRAL:', recordId)
    // Fetch the record data from Exp_Advos table
    // Use the correct primary key with proper escaping for column names with dots
    const filterString = `"N. GRAL".eq."${recordId}"`
    const { data: recordData, error: fetchError } = await supabaseClient
      .from('Exp_Advos')
      .select('*')
      .or(filterString)
      .single()

    if (fetchError) {
      console.error('Error fetching record:', fetchError)
      throw new Error(`Error fetching record: ${fetchError.message}`)
    }

    if (!recordData) {
      throw new Error(`No record found with N. GRAL: ${recordId}`)
    }

    console.log('Record data fetched successfully')
    // Fill the form fields based on the mapping
    fillFormFields(form, recordData)
    console.log('Form fields filled successfully')

    // NO aplanar el formulario para mantenerlo editable
    // form.flatten()

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save()

    // Generate filename with timestamp to avoid cache
    const timestamp = Date.now()
    const fileName = `EX02_${recordData['Nombre1']}_${recordData['1er Apellido1']}_${timestamp}.pdf`

    console.log('Uploading PDF with filename:', fileName)

    // Upload to storage
    const { error: uploadError } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .upload(`generated/${fileName}`, filledPdfBytes, {
        contentType: 'application/pdf',
        upsert: false
      })

    if (uploadError) {
      throw new Error(`Error uploading PDF: ${uploadError.message}`)
    }

    // Get public URL
    const { data: urlData } = supabaseClient.storage
      .from('DOCUMENTOS')
      .getPublicUrl(`generated/${fileName}`)

    return new Response(
      JSON.stringify({
        success: true,
        downloadUrl: urlData.publicUrl,
        fileName: fileName
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error in generate-pdf-ex02:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

function fillFormFields(form: any, data: any) {
  // Helper function para establecer texto de forma segura
  const setText = (fieldName: string, value: any) => {
    try {
      if (form.getField(fieldName) && value !== null && value !== undefined) {
        form.getTextField(fieldName).setText(String(value));
      }
    } catch (e) {
      console.log(`Campo de texto '${fieldName}' no encontrado o error:`, e.message);
    }
  };

  // Helper function para marcar casillas de forma segura
  const setCheckbox = (fieldName: string, checked: boolean) => {
    try {
      const field = form.getCheckBox(fieldName);
      if (field) {
        if (checked) field.check();
        else field.uncheck();
      }
    } catch (e) {
      console.log(`Casilla '${fieldName}' no encontrada o error:`, e.message);
    }
  };

  // --- SECCIÓN: DATOS DE LA PERSONA EXTRANJERA REAGRUPANTE ---
  setText('Texto1', data['PASAPORTE1']);
  setText('Texto3', data['N.I.E1']); // Texto3 es el campo grande para el NIE
  setText('Texto5', data['1er Apellido1']);
  setText('Texto6', data['2º Apellido1']);
  setText('Texto7', data['Nombre1']);

  // Sexo reagrupante
  const sexo1 = data['Sexo1']?.toUpperCase();
  // El HTML no muestra nombres para los checkboxes, pero suelen ser secuenciales.
  // Es crucial verificar estos nombres. Asumiré la secuencia que usaste.
  setCheckbox('Casilla de verificación2', sexo1 === 'H');
  setCheckbox('Casilla de verificación3', sexo1 === 'M');

  // Fecha de nacimiento reagrupante (formato DD/MM/YYYY)
  console.log('=== PROCESANDO FECHA F nac1 ===');
  console.log('Valor de F nac1:', data['F nac1']);
  if (data['F nac1']) {
    const fechaStr = data['F nac1'];
    console.log('fechaStr:', fechaStr);
    const partes = fechaStr.split('/');
    console.log('partes:', partes);
    if (partes.length === 3) {
      const dia = partes[0].padStart(2, '0');
      const mes = partes[1].padStart(2, '0');
      const anio = partes[2];
      console.log(`Asignando fecha: día=${dia}, mes=${mes}, año=${anio}`);
      setText('Texto8', dia);   // Día
      setText('Texto9', mes);   // Mes
      setText('Texto10', anio); // Año
    } else {
      console.log('ERROR: La fecha no tiene 3 partes');
    }
  } else {
    console.log('F nac1 está vacío o undefined');
  }

  setText('Texto11', data['Lugar1']);
  setText('Texto12', data['País1']);
  setText('Texto13', data['Nacionalidad1']);
  
  // Estado civil reagrupante
  const estadoCivil1 = data['Estado civil1']?.toUpperCase();
  setCheckbox('Casilla de verificación5', estadoCivil1 === 'S');
  setCheckbox('Casilla de verificación6', estadoCivil1 === 'C');
  setCheckbox('Casilla de verificación7', estadoCivil1 === 'V');
  setCheckbox('Casilla de verificación8', estadoCivil1 === 'D');
  setCheckbox('Casilla de verificación9', estadoCivil1 === 'SEPARADO' || estadoCivil1 === 'SEP');

  setText('Texto14', data['Padre1']);
  setText('Texto15', data['Madre1']);
  setText('Texto16', data['Domicilio en España1']);
  setText('Texto17', data['Nº1']);
  setText('Texto18', data['Piso1']);
  setText('Texto19', data['Localidad1']);
  setText('Texto20', data['C.P.1']);
  setText('Texto21', data['Provincia1']);
  setText('Texto22', data['Teléfono1']);
  setText('Texto23', data['E-mail1']);
  setText('Texto24', data['TIPO AUTORIZACIÓN']);
  
  const tieneHijos = data['Escolariz2']?.toUpperCase() === 'SI';
  setCheckbox('Casilla de verificación1', tieneHijos);
  setCheckbox('Casilla de verificación4', !tieneHijos);

  // Representante legal (reagrupante): Se deja vacío intencionadamente.
  // Los campos son Texto27, Texto28, Texto29.

  // --- SECCIÓN: DATOS DE LA PERSONA EXTRANJERA REAGRUPADA ---
  // *** MAPEO CORREGIDO BASADO EN EL HTML ***
  setText('Texto30', data['PASAPORTE2']);      // CORREGIDO
  setText('Texto32', data['D.N.I./NIE2']);      // CORREGIDO (usando la caja grande)
  setText('Texto34', data['1er Apellido2']);   // Correcto
  setText('Texto35', data['2º Apellido2']);   // Correcto
  setText('Texto36', data['Nombre2']);         // Correcto

  // Sexo reagrupado/a
  const sexo2 = data['Sexo2']?.toUpperCase();
  setCheckbox('Casilla de verificación11', sexo2 === 'H');
  setCheckbox('Casilla de verificación12', sexo2 === 'M');

  // Fecha de nacimiento reagrupado/a (formato DD/MM/YYYY)
  if (data['F nac2']) {
    const fechaStr2 = data['F nac2'];
    const partes2 = fechaStr2.split('/');
    if (partes2.length === 3) {
      setText('Texto37', partes2[0].padStart(2, '0'));  // Día
      setText('Texto38', partes2[1].padStart(2, '0'));  // Mes
      setText('Texto39', partes2[2]);                   // Año
    }
  }
  
  setText('Texto40', data['Lugar2']);
  setText('Texto41', data['País2']); // Asumo País2, no Nacionalidad2
  setText('Texto42', data['Nacionalidad2']);

  // Estado civil reagrupado/a
  const estadoCivil2 = data['Estado civil2']?.toUpperCase();
  setCheckbox('Casilla de verificación14', estadoCivil2 === 'S');
  setCheckbox('Casilla de verificación15', estadoCivil2 === 'C');
  setCheckbox('Casilla de verificación16', estadoCivil2 === 'V');
  setCheckbox('Casilla de verificación17', estadoCivil2 === 'D');
  setCheckbox('Casilla de verificación18', estadoCivil2 === 'SEPARADO' || estadoCivil2 === 'SEP');
  
  setText('Texto43', data['Padre2']);
  setText('Texto44', data['Madre2']);          // CORREGIDO (antes usabas Texto34)
  setText('Texto45', data['Domicilio en España2']);
  setText('Texto46', data['Nº2']);
  setText('Texto47', data['Piso2']);
  setText('Texto48', data['Localidad2']);
  setText('Texto49', data['C.P.2']);
  setText('Texto50', data['Provincia2']);

  // --- SECCIÓN 2: DATOS DEL REPRESENTANTE A EFECTOS DE PRESENTACIÓN ---
  setText('Texto51', 'MATILDE MÉRIDA RODRIGUEZ');
  setText('Texto52', '30526079L');
  setText('Texto53', 'CALLE ALCALDE SANZ NOGUER');
  setText('Texto54', '3');
  setText('Texto55', '1º3');
  setText('Texto56', 'CORDOBA');
  setText('Texto57', '14005');
  setText('Texto58', 'CORDOBA');
  setText('Texto59', '661754888');
  setText('Texto60', '<EMAIL>');

  // --- SECCIÓN 4: TIPO DE AUTORIZACIÓN SOLICITADA ---
  // (Tu lógica para los checkboxes aquí parece correcta, no la he modificado)
  const tipoAutorizacion = data['TIPO AUTORIZACIÓN']?.toUpperCase() || '';

  if (tipoAutorizacion.includes('REAGRUPACIÓN FAMILIAR') || tipoAutorizacion.includes('REAGRUPACION FAMILIAR')) {
    if (tipoAutorizacion.includes('INICIAL')) {
      setCheckbox('Casilla de verificación20', true);
    } else if (tipoAutorizacion.includes('RENOVACIÓN') || tipoAutorizacion.includes('RENOVACION')) {
      setCheckbox('Casilla de verificación21', true);
    }
  }

  // Residencia independiente
  if (tipoAutorizacion.includes('RESIDENCIA INDEPENDIENTE')) {
    setCheckbox('Casilla de verificación22', true)
  }

  // Autorización de trabajo
  if (tipoAutorizacion.includes('TRABAJO')) {
    if (tipoAutorizacion.includes('CUENTA AJENA')) {
      setCheckbox('Casilla de verificación23', true)
    } else if (tipoAutorizacion.includes('CUENTA PROPIA')) {
      setCheckbox('Casilla de verificación24', true)
    }
  }
}

