import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import JSZip from 'jszip'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DocumentData {
  NOMBRE?: string;
  PRIMER_APELLIDO?: string;
  SEGUNDO_APELLIDO?: string;
  NIE?: string;
  PROVINCIA?: string;
  LOCALIDAD?: string;
  TIPO_DE_VIA?: string;
  NOMBRE_DE_VIA?: string;
  NUMERO?: string;
  PISO?: string;
  LETRA?: string;
  CODIGO_POSTAL?: string;
  TELEFONO?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { recordId } = await req.json()

    if (!recordId) {
      throw new Error('Record ID is required')
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Fetch the record data using the correct primary key
    console.log('Fetching record with N.:', recordId);

    const filterString = `"N.".eq."${recordId}"`;
    const { data: recordData, error: fetchError } = await supabase
      .from('Nacionalidad')
      .select('*')
      .or(filterString)
      .single()

    if (fetchError) {
      throw new Error(`Error fetching record: ${fetchError.message}`)
    }

    // Prepare data for document
    const documentData: DocumentData = {
      NOMBRE: recordData['NOMBRE'],
      PRIMER_APELLIDO: recordData['PRIMER APELLIDO'],
      SEGUNDO_APELLIDO: recordData['SEGUNDO APELLIDO'],
      NIE: recordData['NIE'],
      PROVINCIA: recordData['PROVINCIA'],
      LOCALIDAD: recordData['LOCALIDAD'],
      TIPO_DE_VIA: recordData['TIPO DE VIA'],
      NOMBRE_DE_VIA: recordData['NOMBRE DE VIA'],
      NUMERO: recordData['NUMERO'],
      PISO: recordData['PISO'],
      LETRA: recordData['LETRA'],
      CODIGO_POSTAL: recordData['CODIGO POSTAL'],
      TELEFONO: recordData['TELEFONO']
    }

    // Download original document from storage (as binary buffer)
    const templateBuffer = await downloadOriginalDocumentAsBuffer(supabase);

    // Replace placeholders in the DOCX template
    const completedDocument = await replacePlaceholdersInDocx(templateBuffer, documentData);

    // Upload to Supabase Storage as DOCX file
    const fileName = `OAR_${documentData.NOMBRE || 'SinNombre'}_${documentData.PRIMER_APELLIDO || ''}_${Date.now()}.docx`

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('DOCUMENTOS')
      .upload(`GENERATED/${fileName}`, completedDocument, {
        contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })

    if (uploadError) {
      throw new Error(`Error uploading document: ${uploadError.message}`)
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('DOCUMENTOS')
      .getPublicUrl(`GENERATED/${fileName}`)

    return new Response(
      JSON.stringify({
        success: true,
        downloadUrl: urlData.publicUrl,
        fileName: fileName
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

async function downloadOriginalDocumentAsBuffer(supabase: any): Promise<Uint8Array> {
  try {
    const { data, error } = await supabase.storage
      .from('DOCUMENTOS')
      .download('REPRESENTACION/REPRESENTACION OAR.docx');

    if (error) {
      throw new Error(`Error downloading template: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data received from storage');
    }

    // Convert blob to ArrayBuffer and then to Uint8Array
    const arrayBuffer = await data.arrayBuffer();
    return new Uint8Array(arrayBuffer);
  } catch (error) {
    console.error('Error in downloadOriginalDocumentAsBuffer:', error);
    throw error;
  }
}

// Function to replace placeholders in DOCX using JSZip
async function replacePlaceholdersInDocx(templateBuffer: Uint8Array, data: DocumentData): Promise<Uint8Array> {
  try {
    // Load the DOCX file as a ZIP archive
    const zip = await JSZip.loadAsync(templateBuffer);
    
    // Get the main document content (word/document.xml)
    const documentXml = await zip.file('word/document.xml')?.async('string');
    
    if (!documentXml) {
      throw new Error('Could not find document.xml in DOCX file');
    }
    
    // Replace all placeholders in the XML content
    let modifiedXml = documentXml;
    modifiedXml = modifiedXml.replace(/\{\{NOMBRE\}\}/g, data.NOMBRE || '');
    modifiedXml = modifiedXml.replace(/\{\{PRIMER APELLIDO\}\}/g, data.PRIMER_APELLIDO || '');
    modifiedXml = modifiedXml.replace(/\{\{SEGUNDO APELLIDO\}\}/g, data.SEGUNDO_APELLIDO || '');
    modifiedXml = modifiedXml.replace(/\{\{NIE\}\}/g, data.NIE || '');
    modifiedXml = modifiedXml.replace(/\{\{PROVINCIA\}\}/g, data.PROVINCIA || '');
    modifiedXml = modifiedXml.replace(/\{\{LOCALIDAD\}\}/g, data.LOCALIDAD || '');
    modifiedXml = modifiedXml.replace(/\{\{TIPO DE VIA\}\}/g, data.TIPO_DE_VIA || '');
    modifiedXml = modifiedXml.replace(/\{\{NOMBRE DE VIA\}\}/g, data.NOMBRE_DE_VIA || '');
    modifiedXml = modifiedXml.replace(/\{\{NUMERO\}\}/g, data.NUMERO || '');
    modifiedXml = modifiedXml.replace(/\{\{PISO\}\}/g, data.PISO || '');
    modifiedXml = modifiedXml.replace(/\{\{LETRA\}\}/g, data.LETRA || '');
    modifiedXml = modifiedXml.replace(/\{\{CODIGO POSTAL\}\}/g, data.CODIGO_POSTAL || '');
    modifiedXml = modifiedXml.replace(/\{\{TELEFONO\}\}/g, data.TELEFONO || '');
    
    // Add current date
    const currentDate = new Date().toLocaleDateString('es-ES', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric' 
    });
    modifiedXml = modifiedXml.replace(/\(fecha actual\)/g, currentDate);
    
    // Update the document.xml in the ZIP
    zip.file('word/document.xml', modifiedXml);
    
    // Generate the modified DOCX file
    const modifiedDocx = await zip.generateAsync({ type: 'uint8array' });
    
    return modifiedDocx;
  } catch (error) {
    console.error('Error replacing placeholders in DOCX:', error);
    throw error;
  }
}

