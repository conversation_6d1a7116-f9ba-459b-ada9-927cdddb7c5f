// add-record.js - Lógica para añadir nuevos registros

document.addEventListener('DOMContentLoaded', () => {
    const tableHeaders = {
        'Exp_Advos': ['enlace', 'N. GRAL', 'TIPO AUTORIZACIÓN', 'EXPEDIENTE', 'F. DE PRESENTACIÓN', 'PASAPORTE1', 'N.I.E1', '1er Apellido1', '2º Apellido1', 'Nombre1', 'Sexo1', 'F nac1', 'Lugar1', 'País1', 'Nacionalidad1', 'Estado civil1', 'Padre1', 'Madre1', 'Domicilio en España1', 'Nº1', 'Bl1', 'Piso1', 'Localidad1', 'C.P.1', 'Provincia1', 'Teléfono1', 'E-mail1', 'Rep. Legal2', 'DNI/NIE/PAS2', 'PASAPORTE2', 'D.N.I./NIE2', '1er Apellido2', '2º Apellido2', 'Nombre2', 'Sexo2', 'F nac2', 'Lugar2', 'País2', 'Nacionalidad2', 'Estado civil2', 'Padre2', 'Madre2', 'Domicilio en España2', 'Nº2', 'Piso2', 'Localidad2', 'C.P.2', 'Provincia2', 'Vínculo2', 'Autorizacion2', 'Escolariz2', 'NIF/CIF3', 'Nombre/Razón Socia3', 'Actividad3', 'Ocupación3', 'Domicilio3', 'Localidad3', 'C.P.3', 'Provincia3', 'Teléfono3', 'E-mail3', 'Representante3', 'DNI/NIE/PAS3', 'Título3', 'Domicilio en España3_Rep', 'Nº3_Rep', 'Piso3_Rep', 'Localidad3_Rep', 'C.P.3_Rep', 'Provincia3_Rep', 'Teléfono3_Rep', 'E-mail3_Rep', 'FECHA3', 'F. RESOLUCION3', 'F. NOTIFICACION3', 'F. RECURSO3', 'DECISION3', 'AUTORIZACION3'],
        'Nacionalidad': ['enlace', 'N.', 'NIE', 'Nº Soporte', 'NOMBRE', 'PRIMER APELLIDO', 'SEGUNDO APELLIDO', 'E CIVIL', 'SEXO', 'PAIS DE NACIMIENTO', 'LOCALIDAD DE NACIMIENTO', 'FECHA DE NACIMIENTO', 'FECHA RESIDENCIA', 'PADRE', 'MADRE', 'PROVINCIA', 'LOCALIDAD', 'TIPO DE VIA', 'NOMBRE DE VIA', 'NUMERO', 'PISO', 'LETRA', 'CODIGO POSTAL', 'TELEFONO', 'CORREO ELECTRONICO', 'NOMBRE2', 'APELLIDO2', '2º APELLIDO2', 'FECHA2', 'ESPOSO2', 'DNI/NIE2', 'NACIONALIDAD2', 'HIJO 1', 'APELLIDO HIJO 1', 'F NACIMIENTO HIJO 1', 'LUGAR HIJO 1', 'PAIS HIJO 1', 'HIJO 2', 'APELLLIDO HIJO 2', 'FECHA HIJO 2', 'LUGAR HIJO 2', 'PAIS HIJO 2', 'HIJO 3', 'APELLIDO HIJO 3', 'FECHA HIJO 3', 'LUGAR HIJO 3', 'PAIS HIJO 3'],
        'Proc_Judiciales': ['enlace', 'N. GRAL', 'NOMBRE', 'NIE/PASAPORTE', 'NACIONALIDAD', 'DOMICILIO', 'NÚMERO', 'MUNICIPIO', 'CÓDIGO POSTAL', 'PROVINCIA', 'TELEFONO', 'CORREO ELECTRONICO', 'EXPEDIENTE', 'OBJETO', 'F. SOLICITUD', 'FECHA', 'NOTIFICADA', 'MOTIVO 1', 'JUSTIFICACIÓN', 'FECHA DEMANDA', 'JUZGADO', 'NÚMERO DE PROCEDIMIENTO'],
        'Datos_Generales': ['enlace', 'NÚM GRAL', 'UBICACIÓN', 'PROCEDIMIENTO', 'NOMBRE', 'ESTADO', 'COMENTARIO']
    };

    const addRecordForm = document.getElementById('add-record-form');
    const tableSelect = document.getElementById('add-table-select');
    const fieldsContainer = document.getElementById('form-fields-container');
    const clearFormButton = document.getElementById('clear-form-btn');
    const loader = document.getElementById('add-loader');
    const message = document.getElementById('add-message');

    function generateFormFields(tableName) {
        if (!fieldsContainer) return;
        fieldsContainer.innerHTML = ''; 
        const headers = tableHeaders[tableName];
        if (!headers) return;

        headers.forEach(header => {
            if (header.toLowerCase() === 'id' || header.toLowerCase() === 'enlace') return;
            const fieldGroup = document.createElement('div');
            fieldGroup.className = 'form-group';
            const label = document.createElement('label');
            label.textContent = header;
            label.htmlFor = `field-${header}`;
            const input = document.createElement('input');
            input.type = 'text';
            input.id = `field-${header}`;
            input.name = header;
            input.placeholder = `Introduzca ${header}...`;
            fieldGroup.appendChild(label);
            fieldGroup.appendChild(input);
            fieldsContainer.appendChild(fieldGroup);
        });
    }

    async function handleFormSubmit(event) {
        event.preventDefault();
        const tableName = tableSelect.value;
        const formData = new FormData(addRecordForm);
        const newRecord = {};

        // Debug: mostrar qué tabla se está usando
        console.log('Tabla seleccionada:', tableName);

        for (const [key, value] of formData.entries()) {
            // Filtrar campos problemáticos y el campo 'table'
            if (key !== 'table' && key !== '2' && key !== '3' && key.trim() !== '') {
                newRecord[key] = value.trim() === '' ? null : value.trim();
            }
        }

        // Debug: mostrar los datos que se van a insertar
        console.log('Datos a insertar:', newRecord);

        loader.classList.remove('hidden');
        message.textContent = '';

        try {
            const { error } = await supabaseClient.from(tableName).insert([newRecord]);
            loader.classList.add('hidden');

            if (error) {
                console.error('Error de Supabase:', error);
                message.textContent = `Error: ${error.message}`;
                message.style.color = 'red';
            } else {
                message.textContent = '¡Registro guardado con éxito!';
                message.style.color = 'green';
                addRecordForm.reset();
                generateFormFields(tableName);
            }
        } catch (err) {
            loader.classList.add('hidden');
            console.error('Error general:', err);
            message.textContent = `Error: ${err.message}`;
            message.style.color = 'red';
        }

        setTimeout(() => { message.textContent = ''; }, 5000);
    }

    // --- CORRECCIÓN DE ERROR: Comprobar si los elementos existen antes de añadir listeners ---
    if (addRecordForm) {
        tableSelect.addEventListener('change', () => {
            generateFormFields(tableSelect.value);
            addRecordForm.reset();
            message.textContent = '';
        });

        clearFormButton.addEventListener('click', () => {
            addRecordForm.reset();
            message.textContent = '';
            generateFormFields(tableSelect.value);
        });

        addRecordForm.addEventListener('submit', handleFormSubmit);

        // Inicialización
        generateFormFields(tableSelect.value);
    }
});