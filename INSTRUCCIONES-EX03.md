# Instrucciones para completar la integración del EX03

## ✅ Archivos creados

He creado los siguientes archivos para añadir el soporte del formulario EX03:

1. **`supabase/functions/inspect-pdf-ex03/index.ts`**
   - Función Edge para inspeccionar los campos del PDF EX03
   - Útil para verificar los nombres de los campos del formulario

2. **`supabase/functions/generate-pdf-ex03/index.ts`**
   - Función Edge principal para generar el PDF EX03 rellenado
   - Obtiene datos de la tabla `Exp_Advos`
   - Rellena automáticamente los campos del formulario
   - Sube el PDF generado al storage

3. **`EX03-campos-formulario.md`**
   - Documentación completa del mapeo de campos
   - Referencia de qué columnas de la BD se usan para cada campo
   - Notas sobre campos que no se rellenan automáticamente

## ✅ Archivos modificados

1. **`document-generator.js`**
   - Añadida la opción `ex03` en la función `handleGenerateDocument`
   - Ahora llama a `generate-pdf-ex03` cuando se selecciona EX03

2. **`script.js`**
   - Añadida la opción "EX03 (PDF)" en el menú desplegable de generación de documentos
   - Disponible para la tabla Exp_Advos

## 📋 Pasos pendientes para completar la integración

### 1. Subir el PDF EX03 al Storage de Supabase

Necesitas subir el archivo `documentos/EX03.pdf` al bucket de Supabase:

**Opción A: Usando la interfaz web de Supabase**
1. Ve a https://supabase.com/dashboard/project/twxtlrjbpxifcdzepxfk/storage/buckets/DOCUMENTOS
2. Navega a la carpeta `EX`
3. Sube el archivo `EX03.pdf` desde `documentos/EX03.pdf`

**Opción B: Usando Supabase CLI**
```bash
supabase storage cp "documentos/EX03.pdf" DOCUMENTOS/EX/EX03.pdf
```

### 2. Desplegar las funciones Edge a Supabase

Necesitas desplegar las nuevas funciones Edge:

```bash
# Desplegar la función de inspección (opcional, solo para debugging)
supabase functions deploy inspect-pdf-ex03

# Desplegar la función de generación (REQUERIDO)
supabase functions deploy generate-pdf-ex03
```

### 3. Verificar que funciona

1. Abre la aplicación en el navegador
2. Ve a la tabla `Exp_Advos`
3. En cualquier registro, haz clic en el menú desplegable "Generar Doc..."
4. Selecciona "EX03 (PDF)"
5. Debería generarse y descargarse el PDF rellenado

## 🔍 Mapeo de datos

### Datos que se rellenan automáticamente:

**Sección 1 - Datos del Solicitante:**
- Pasaporte, NIE, Apellidos, Nombre
- Fecha de nacimiento, Lugar, País, Nacionalidad
- Nombre del padre y madre
- Domicilio completo en España
- Teléfono y Email

**Sección 2 - Datos del Empleador:**
- Nombre/Razón Social del empleador
- NIF/CIF
- Actividad
- Domicilio (parcial - falta número y piso)
- Localidad, CP, Provincia
- Teléfono y Email
- Datos del representante del empleador

**Sección 4 - Datos del Representante:**
- Datos fijos de Matilde Mérida Rodríguez
- Nombre, DNI, Domicilio completo
- Teléfono y Email

### Datos que NO se rellenan (requieren entrada manual):

**Sección 3 - Datos del Contrato de Trabajo:**
- Denominación del puesto
- Grupo de cotización
- CNO SEPE 2011
- Código y denominación del convenio
- Código y denominación del contrato
- Código cuenta de cotización
- Retribución bruta
- Dirección del centro de trabajo

**Sección 5 - Domicilio a efectos de notificaciones:**
- Todos los campos de esta sección

**Motivo:** Estos datos no están disponibles en la tabla `Exp_Advos` de la base de datos.

## 🔧 Solución de problemas

### Si el PDF no se genera:

1. **Verificar que el PDF está en el storage:**
   - Ve a Supabase Dashboard → Storage → DOCUMENTOS → EX
   - Debe existir el archivo `EX03.pdf`

2. **Verificar que la función está desplegada:**
   ```bash
   supabase functions list
   ```
   Debe aparecer `generate-pdf-ex03` en la lista

3. **Ver los logs de la función:**
   ```bash
   supabase functions logs generate-pdf-ex03
   ```

4. **Verificar los datos del registro:**
   - Asegúrate de que el registro tiene datos en las columnas necesarias
   - Especialmente: `Nombre1`, `1er Apellido1`, `PASAPORTE1`, `N.I.E1`

### Si algunos campos no se rellenan:

1. Verifica que las columnas existen en la tabla `Exp_Advos`
2. Verifica que el registro tiene datos en esas columnas
3. Consulta el archivo `EX03-campos-formulario.md` para ver el mapeo exacto

## 📝 Notas adicionales

1. **El PDF se mantiene editable:** No se aplana el formulario, por lo que puedes editar manualmente cualquier campo después de generarlo.

2. **Campos faltantes en la BD:** Algunos campos del formulario no tienen correspondencia en la base de datos:
   - CNAE del empleador
   - Número y piso del domicilio del empleador
   - Todos los datos del contrato de trabajo
   - Domicilio de notificaciones

3. **Posibles mejoras futuras:**
   - Añadir columnas a la tabla `Exp_Advos` para los datos del contrato
   - Crear una tabla separada para datos de contratos
   - Añadir validación de datos antes de generar el PDF

## ✨ Resumen

La integración del EX03 está completa en el código. Solo necesitas:
1. ✅ Subir el PDF al storage de Supabase
2. ✅ Desplegar la función `generate-pdf-ex03`
3. ✅ Probar la generación desde la interfaz

¡Todo listo para usar! 🎉

