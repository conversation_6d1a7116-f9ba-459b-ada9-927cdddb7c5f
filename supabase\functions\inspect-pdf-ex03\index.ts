import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Download the PDF from storage
    const { data, error } = await supabase.storage
      .from('DOCUMENTOS')
      .download('EX/EX03.pdf')

    if (error) {
      throw new Error(`Error downloading PDF: ${error.message}`)
    }

    // Convert blob to ArrayBuffer
    const arrayBuffer = await data.arrayBuffer()
    const pdfBytes = new Uint8Array(arrayBuffer)

    // Load the PDF
    const pdfDoc = await PDFDocument.load(pdfBytes)

    // Get the form
    const form = pdfDoc.getForm()
    const fields = form.getFields()

    // Extract field information with more details
    const fieldInfo = fields.map((field, index) => {
      const fieldData: any = {
        index: index + 1,
        name: field.getName(),
        type: field.constructor.name
      }

      // Try to get additional info for text fields
      try {
        if (field.constructor.name === 'PDFTextField2') {
          const textField = field as any
          fieldData.maxLength = textField.getMaxLength?.() || 'unlimited'
        }
      } catch (e) {
        // Ignore errors
      }

      return fieldData
    })

    return new Response(
      JSON.stringify({
        success: true,
        totalFields: fields.length,
        fields: fieldInfo
      }, null, 2),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

