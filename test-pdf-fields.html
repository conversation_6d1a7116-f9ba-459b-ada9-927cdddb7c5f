<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inspeccionar Campos PDF</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        #result {
            margin-top: 20px;
            padding: 20px;
            background-color: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Inspeccionar Campos del PDF EX10</h1>
    <button onclick="inspectPDF()">Inspeccionar Campos</button>
    <div id="result"></div>

    <script>
        // Reemplaza con tu clave anónima de Supabase
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR3eHRscmpicHhpZmNkemVweGZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTk0MzAwODgsImV4cCI6MjA3NTAwNjA4OH0.ycUkKeHQ7c2bPBA01EN1bi70kIEfS7D_Uc4K6FA1YNA';

        async function inspectPDF() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = 'Inspeccionando PDF...';

            try {
                const response = await fetch('https://twxtlrjbpxifcdzepxfk.supabase.co/functions/v1/inspect-pdf-fields', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
                    }
                });

                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>

