import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { PDFDocument } from 'https://cdn.skypack.dev/pdf-lib@1.17.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { recordId } = await req.json()

    if (!recordId) {
      throw new Error('recordId is required')
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    console.log('Downloading PDF template...')
    // Download the PDF template from storage
    const { data, error } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .download('EX/EX03.pdf')

    if (error) {
      console.error('Error downloading PDF:', error)
      throw new Error(`Error downloading PDF: ${error.message}`)
    }

    console.log('Loading PDF document...')
    // Load the PDF
    const pdfBytes = await data.arrayBuffer()
    const pdfDoc = await PDFDocument.load(pdfBytes)

    // Get the form
    const form = pdfDoc.getForm()
    console.log('PDF form loaded successfully')

    console.log('Fetching record data for N. GRAL:', recordId)
    // Fetch the record data from Exp_Advos table
    const filterString = `"N. GRAL".eq."${recordId}"`
    const { data: recordData, error: fetchError } = await supabaseClient
      .from('Exp_Advos')
      .select('*')
      .or(filterString)
      .single()

    if (fetchError) {
      console.error('Error fetching record:', fetchError)
      throw new Error(`Error fetching record: ${fetchError.message}`)
    }

    if (!recordData) {
      throw new Error(`No record found with N. GRAL: ${recordId}`)
    }

    console.log('Record data fetched successfully')
    // Fill the form fields based on the mapping
    fillFormFields(form, recordData)
    console.log('Form fields filled successfully')

    // NO aplanar el formulario para mantenerlo editable
    // form.flatten()

    // Save the filled PDF
    const filledPdfBytes = await pdfDoc.save()

    // Generate filename with timestamp to avoid cache
    const timestamp = Date.now()
    const fileName = `EX03_${recordData['Nombre1']}_${recordData['1er Apellido1']}_${timestamp}.pdf`

    console.log('Uploading PDF with filename:', fileName)

    // Upload to storage
    const { error: uploadError } = await supabaseClient.storage
      .from('DOCUMENTOS')
      .upload(`generated/${fileName}`, filledPdfBytes, {
        contentType: 'application/pdf',
        upsert: false
      })

    if (uploadError) {
      throw new Error(`Error uploading PDF: ${uploadError.message}`)
    }

    // Get public URL
    const { data: urlData } = supabaseClient.storage
      .from('DOCUMENTOS')
      .getPublicUrl(`generated/${fileName}`)

    return new Response(
      JSON.stringify({
        success: true,
        downloadUrl: urlData.publicUrl,
        fileName: fileName
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    console.error('Error in generate-pdf-ex03:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

function fillFormFields(form, data) {
  console.log('=== INICIANDO RELLENO DE CAMPOS EX03 - VERSIÓN ACTUALIZADA ===')
  console.log('Datos recibidos:', JSON.stringify(data, null, 2))

  // Helper para establecer texto de forma segura, evitando errores si un campo no existe.
  const setText = (fieldName, value) => {
    try {
      if (form.getField(fieldName) && value !== null && value !== undefined) {
        form.getTextField(fieldName).setText(String(value));
      }
    } catch (e) {
      console.log(`Campo de texto '${fieldName}' no encontrado o error:`, e.message);
    }
  };

  // Helper para marcar casillas de forma segura.
  const setCheckbox = (fieldName, checked) => {
    try {
      const field = form.getCheckBox(fieldName);
      if (field) {
        if (checked) field.check();
        else field.uncheck();
      }
    } catch (e) {
      console.log(`Casilla '${fieldName}' no encontrada o error:`, e.message);
    }
  };

  // --- SECCIÓN 1: DATOS DE LA PERSONA EXTRANJERA ---
  setText('Texto1', data['PASAPORTE1']);
  setText('Texto3', data['N.I.E1']);
  setText('Texto5', data['1er Apellido1']);
  setText('Texto6', data['2º Apellido1']);
  setText('Texto7', data['Nombre1']);
  
  // Fecha de nacimiento del solicitante (formato DD/MM/YYYY)
  if (data['F nac1']) {
    const fechaStr = data['F nac1'];
    console.log('Procesando fecha de nacimiento:', fechaStr);

    // Parsear fecha en formato DD/MM/YYYY
    const partes = fechaStr.split('/');
    if (partes.length === 3) {
      const dia = partes[0].padStart(2, '0');
      const mes = partes[1].padStart(2, '0');
      const anio = partes[2];

      setText('Texto8', dia);
      setText('Texto9', mes);
      setText('Texto10', anio);
      console.log(`Fecha parseada: día=${dia}, mes=${mes}, año=${anio}`);
    } else {
      console.log(`Formato de fecha no reconocido: ${fechaStr}`);
    }
  }
  
  setText('Texto11', data['Lugar1']);
  setText('Texto12', data['País1']);
  setText('Texto13', data['Nacionalidad1']);
  setText('Texto14', data['Padre1']);
  setText('Texto15', data['Madre1']);
  setText('Texto16', data['Domicilio en España1']);
  setText('Texto17', data['Nº1']);
  setText('Texto18', data['Piso1']);
  setText('Texto19', data['Localidad1']);
  setText('Texto20', data['C.P.1']);
  setText('Texto21', data['Provincia1']);
  setText('Texto22', data['Teléfono1']);
  setText('Texto23', data['E-mail1']);
  
  // --- SECCIÓN 2: DATOS DEL EMPLEADOR/A ---
  // !! CORRECCIÓN: Se han actualizado los nombres de los campos según el HTML del PDF.
  setText('Texto37', data['Nombre/Razón Socia3']);
  setText('Texto38', data['NIF/CIF3']);
  setText('Texto39', data['Actividad3']);
  setText('Texto41', data['Domicilio3']); // Domicilio Social
  setText('Texto44', data['Localidad3']);
  setText('Texto45', data['C.P.3']);
  setText('Texto46', data['Provincia3']);
  setText('Texto47', data['Teléfono3']);
  setText('Texto48', data['E-mail3']);
  setText('Texto50', data['DNI/NIE/PAS3']); // DNI/NIE del representante del empleador
  setText('Texto51', data['Título3']); // Título del representante del empleador

  // --- SECCIÓN 3: DATOS DEL CONTRATO DE TRABAJO ---
  // Esta sección se deja vacía según el código original.
  
  // --- SECCIÓN 4: DATOS DEL REPRESENTANTE A EFECTOS DE PRESENTACIÓN ---
  // !! CORRECCIÓN: Se han actualizado los nombres de los campos y asignado los datos fijos.
  setText('Texto67', 'MATILDE MÉRIDA RODRIGUEZ');      // Nombre/Razón Social
  setText('Texto68', '30526079L');                   // DNI/NIE/PAS
  setText('Texto69', 'CALLE ALCALDE SANZ NOGUER');    // Domicilio en España
  setText('Texto70', '3');                          // Nº
  setText('Texto71', '1º3');                         // Piso
  setText('Texto72', 'CORDOBA');                      // Localidad
  setText('Texto73', '14005');                      // C.P.
  setText('Texto74', 'CORDOBA');                      // Provincia
  setText('Texto75', '661754888');                   // Teléfono móvil (OJO: el nombre del campo es Texto72)
  setText('Texto76', '<EMAIL>'); // E-mail (OJO: el nombre del campo es Texto73)
}
